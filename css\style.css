
@font-face {
    font-family: 'Chat';
    src: url('https://reich.vip/corleone/v3/web/fonts/chalet.otf');
}

@font-face {
    font-family: 'Arame-Mono';
    src: url('./fonts/Arame-Mono/Arame-Mono.otf');
}

@font-face {
    font-family: 'EasyOpenFace';
    src: url('./fonts/EasyOpenFace/EasyOpenFace.ttf');
}

@font-face {
    font-family: 'Valo';
    src: url('./fonts/Valo/Valo.ttf');
}

@font-face {
    font-family: 'Gilroy-Regular';
    src: url('./fonts/Gilroy-Regular/Gilroy-Regular.ttf');
}

@font-face {
    font-family: 'Avant Garde Book BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Book BT'), url('./fonts/Avgardd/AVGARDN_2.woff') format('woff');
}

@font-face {
    font-family: 'Avant Garde Demi BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Demi BT'), url('./fonts/Avgardd/AVGARDD_2.woff') format('woff');
}


@font-face {
    font-family: 'Avant Garde Demi Oblique BT';
    font-style: normal;
    font-weight: normal;
    src: local('Avant Garde Demi Oblique BT'), url('./fonts/Avgardd/AVGARDDO_2.woff') format('woff');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 400;
    src: url('./fonts/Gilroy/Gilroy-Regular.ttf');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 500;
    src: url('./fonts/Gilroy/Gilroy-Medium.ttf');
}

@font-face {
    font-family: Gilroy123;
    font-style: normal;
    font-weight: 600;
    src: url('./fonts/Gilroy/Gilroy-SemiBold.ttf');
}

@font-face {
    font-family: AquireBold-8Ma60;
    src: url(./fonts/AquireBold/AquireBold-8Ma60.otf);
}

@font-face {
    font-family: proximanova;
    src: url(./fonts/proximanova/proximanova_extrabold.otf);
}

@font-face {
    font-family: Evogria;
    src: url(./fonts/Evogria/Evogria.otf);
}

:root {
    --clr-white: #fff;
    --clr-blue: #ff2323;
    /* --clr-blue: #2377ff; */
    --clr-red: #ff2323;
    --clr-money-green: #ace760;
    --clr-money-green-icon: #ade76073;
    --clr-black-money-red: #e76060;
    --clr-black-money-red-icon: #e7606073;
    --clr-weapon-border: #cb4f4f;
    --clr-weapon-box-shadow: #cb4f4f83;
    --clr-notify-item-success: #ace760;
    --clr-notify-item-info: #ffa136;
    --clr-notify-item-error: #ff3636;
    --clr-notify-item-ooc: #ff2323;
    --clr-announce-item: #ff2323;
    --clr-teamchat-item: #ff2323;
    --clr-speedo-needle: #ff2323;
    --clr-speedo-point: #ff2323;
    --clr-speedo-flex-active: #43ff36;
    --clr-speedo-bg: #36ff47;
    --clr-speedo-fuel-icon: #36ff47;
    --clr-rpm-bg: #fff;
    --clr-progress-inner: #2377ff;
    --clr-foodhud-food-bg: #cb8b4f;
    --clr-foodhud-food-box-shadow: #cb8b4f;
    --clr-foodhud-thirst-bg: #4f83cb;
    --clr-foodhud-thirst-box-shadow: #4f76cb;
    --clr-settings-bg: #070707ed;
    --clr-settings-btn-active: #cb4f4f;
    --clr-settings-save-btn: #43ff36;
    --clr-settings-reset-btn: #ff3636;
    --clr-voice-red: #ff3636;
    --clr-voice-green: #4eff36;
    --clr-speedo-bar-gear: #d73232;
    --clr-speedo-bar-item-active: #43ff36;
    --clr-speedo-bar-fuel-progress: #20ef41;

    --ff-aquire-bold: 'AquireBold-8Ma60', sans-serif;
    --ff-header: 'SVN-Gilroy', sans-serif;
    --ff-druk: 'Druk Trial', sans-serif;
    --ff-agency: 'Agency FB', sans-serif;
    --border-radius-frame: 1vh;
    --border-radius-close: 0.5vh;
    --color-white: rgba(255, 255, 255, 1);
    --color-opacity-white: rgba(255, 255, 255, 0.5);
    --ff-passport: 'Abhaya Libre', serif;
    --ff-rageui: 'Chat';
    --ff-akrobat: 'Akrobat', sans-serif;
    --ff-inter: 'Inter', sans-serif;
    --ff-special: 'Gilroy-Regular', sans-serif;
    --ff-rad: 'Rajdhani', sans-serif;
    --ff-mons: 'Montserrat', sans-serif;
    --ff-bebas: 'Bebas Neue', sans-serif;
    --ff-gilroy: 'Gilroy', sans-serif;
    --ff-valo: 'Valo', Arial, Helvetica, sans-serif;
    --ff-gangwar: 'Avant Garde Demi Oblique BT', sans-serif;
    --ff-gangwar-2: 'Avant Garde Book BT', sans-serif;
    --ff-giloryblack: 'Gilroy-BlackItalic';
    --ff-bit: 'VCR OSD Mono', sans-serif;
    --ff-body: 'Roboto', sans-serif;
    --ff-proximanova: 'proximanova', sans-serif;
    --ff-evogria: 'Evogria', sans-serif;
    --clr-pink: #bc1156;
    --clr-orange: #eb5757;
    --clr-new-blue: #3266d7;
    --clr-new-blue-2: #453fbd;
    --clr-new-orange: #ff2323;
    --clr-new-2-orange: #fc6e6e;
    --piv: 0.09259259259259259vh;

    --ff-special: 'Rajdhani', sans-serif;

    --px-in-vh: 0.10460251046025104vh;
}

*,
*::before,
*::after {
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    user-select: none;
}