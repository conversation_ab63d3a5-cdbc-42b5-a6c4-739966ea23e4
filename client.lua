-- U21 Frontend Client Script
local isUIOpen = false

-- Initialize NUI
Citizen.CreateThread(function()
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "hide"
    })
end)

-- Function to show UI
function ShowUI()
    if not isUIOpen then
        isUIOpen = true
        SetNuiFocus(true, true)
        SendNUIMessage({
            action = "show"
        })
    end
end

-- Function to hide UI
function HideUI()
    if isUIOpen then
        isUIOpen = false
        SetNuiFocus(false, false)
        SendNUIMessage({
            action = "hide"
        })
    end
end

-- NUI Callbacks
RegisterNUICallback('close', function(data, cb)
    HideUI()
    cb('ok')
end)

-- Command to open UI (for testing)
RegisterCommand('openui', function()
    ShowUI()
end, false)

-- Command to close UI
RegisterCommand('closeui', function()
    HideUI()
end, false)

-- Export functions
exports('ShowUI', ShowUI)
exports('HideUI', HideUI)

-- Event handlers
RegisterNetEvent('u21_frontend:show')
AddEventHandler('u21_frontend:show', function()
    ShowUI()
end)

RegisterNetEvent('u21_frontend:hide')
AddEventHandler('u21_frontend:hide', function()
    HideUI()
end)

-- Update HUD data
RegisterNetEvent('u21_frontend:updateHUD')
AddEventHandler('u21_frontend:updateHUD', function(data)
    SendNUIMessage({
        action = "updateHUD",
        data = data
    })
end)
