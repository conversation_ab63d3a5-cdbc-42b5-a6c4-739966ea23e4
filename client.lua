-- U21 Frontend Client Script
local isUIOpen = false

-- Initialize when player spawns
Citizen.CreateThread(function()
    while true do
        Citizen.Wait(1000)
        if NetworkIsPlayerActive(PlayerId()) then
            print("^2[U21 Frontend] Script loaded successfully!")
            SetNuiFocus(false, false)
            break
        end
    end
end)

-- Function to show UI
function ShowUI()
    print("^3[U21 Frontend] Showing UI...")
    isUIOpen = true
    SetNuiFocus(true, true)
    SendNUIMessage({
        action = "show"
    })
end

-- Function to hide UI
function HideUI()
    print("^3[U21 Frontend] Hiding UI...")
    isUIOpen = false
    SetNuiFocus(false, false)
    SendNUIMessage({
        action = "hide"
    })
end

-- NUI Callbacks
RegisterNUICallback('close', function(data, cb)
    print("^3[U21 Frontend] NUI Close callback received")
    HideUI()
    cb('ok')
end)

-- Command to open UI (for testing)
RegisterCommand('openui', function()
    print("^2[U21 Frontend] OpenUI command executed")
    ShowUI()
end, false)

-- Command to close UI
RegisterCommand('closeui', function()
    print("^2[U21 Frontend] CloseUI command executed")
    HideUI()
end, false)

-- Test command to check if script is working
RegisterCommand('testui', function()
    print("^1[U21 Frontend] Test command - Script is working!")
    TriggerEvent('chat:addMessage', {
        color = {255, 0, 0},
        multiline = true,
        args = {"U21 Frontend", "Script is loaded and working!"}
    })
end, false)
