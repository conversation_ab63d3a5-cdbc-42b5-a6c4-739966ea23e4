// U21 Frontend Script
$(document).ready(function() {
    // NUI Message Handler
    window.addEventListener('message', function(event) {
        const data = event.data;
        
        switch(data.action) {
            case 'show':
                $('body').fadeIn(500);
                break;
            case 'hide':
                $('body').fadeOut(500);
                break;
            case 'updateHUD':
                updateHUD(data);
                break;
        }
    });

    // Close on ESC key
    $(document).keyup(function(e) {
        if (e.keyCode === 27) { // ESC key
            $.post('https://u21_frontend/close', JSON.stringify({}));
        }
    });

    // Update HUD function
    function updateHUD(data) {
        // Add your HUD update logic here
        console.log('HUD Update:', data);
    }

    // Initialize
    $('body').hide();
});

// Post to NUI Callback
function postNUI(endpoint, data) {
    $.post(`https://u21_frontend/${endpoint}`, JSON.stringify(data));
}
