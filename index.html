<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <script src="https://kit.fontawesome.com/96a2c6323b.js" crossorigin="anonymous"></script>
    <link rel="stylesheet" href="css/style.css">
    <title>by cscripts</title>
</head>
<body style="background-color: rgba(255, 0, 0, 0);">
    <script src="./js/jquery-3.6.0.js"></script>
    <script src="./js/jquery-ui.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.6.0/jquery.min.js" integrity="sha512-894YE6QWD5I59HgZOGReFYm4dnWc1Qt5NtvYSaNcOP+u1T9qYdvdihz0PPSiiqn/+/3e7Jo4EaG7TubfWGUrMQ==" crossorigin="anonymous" referrerpolicy="no-referrer"></script>
    <script src="js/script.js" crossorigin="anonymous"></script>
    <style>


        .main__hud-top-container-information {
            position: absolute;
            top: 3vh;
            right: 3vh;
            z-index: -2;
            font-family: var(--ff-proximanova) !important;
            font-weight: 600;
            transform: skew(-10deg);
            text-transform: uppercase;
            font-size: 2.8vh;
            color: var(--clr-white);
            text-shadow: 0.35vh 0.35vh 0vh rgba(0, 0, 0, 0.25);
            text-shadow: 0vh 0vh 1.7vh var(--clr-white);
            letter-spacing: 0.6vh;
        }

        .main__hud-top-container-information p span {
            color: var(--clr-new-orange);
            text-shadow: 0vh 0vh 1.7vh var(--clr-new-orange);
        }









        /* PRADA CITY HUD Styles */
        .main__hud-container {
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 9999;
            display: block !important;
        }

        .main__hud-top-container-information {
            background: rgba(0, 0, 0, 0.8);
            padding: 15px 25px;
            border-radius: 8px;
            border: 2px solid #c11717;
            box-shadow: 0 0 20px rgba(193, 23, 23, 0.5);
        }

        .main__hud-top-container-information p {
            margin: 0;
            font-size: 24px;
            font-weight: bold;
            text-transform: uppercase;
            letter-spacing: 2px;
            color: #ffffff;
            text-shadow: 0 0 5px #ffffff;
        }

        .main__hud-top-container-information span {
            color: #ff4848;
            text-shadow: 0 0 10px #ff4848;
        }
    </style>
    <main>
         <!-- FINAL CITY HUD 80% -->
        <div class="main__hud-container" style="display: block;">
            <div class="main__hud-top-container-information">
                <p><span>Prada</span>CITY</p>
            </div>


        <!-- FINAL CITY HUD 100% -->