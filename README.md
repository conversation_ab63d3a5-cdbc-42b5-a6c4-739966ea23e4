# U21 Frontend - PRADA CITY

## Installation

1. <PERSON><PERSON><PERSON><PERSON> den Ordner in deinem FiveM Server unter `resources/`
2. <PERSON><PERSON><PERSON> `ensure u21_frontend` zu deiner `server.cfg` hinzu
3. Starte den Server neu

## Verwendung

### Commands (Ingame):
- `/openui` - Öffnet das UI
- `/closeui` - Schließt das UI
- `ESC` - Schließt das UI

### Für andere Scripts:
```lua
-- UI anzeigen
TriggerEvent('u21_frontend:show')

-- UI verstecken
TriggerEvent('u21_frontend:hide')

-- HUD Daten aktualisieren
TriggerEvent('u21_frontend:updateHUD', {
    -- <PERSON><PERSON> hier
})
```

### Exports:
```lua
-- UI anzeigen
exports['u21_frontend']:ShowUI()

-- UI verstecken
exports['u21_frontend']:HideUI()
```

## Features

- Responsive Design
- PRADA CITY Branding
- NUI Integration
- Event System
- Export Functions

## Author
cscripts