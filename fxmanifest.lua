fx_version 'cerulean'
game 'gta5'

author 'cscripts'
description 'U21 Frontend - Advanced HUD System'
version '1.0.0'

-- UI Files
ui_page 'index.html'

files {
    'index.html',
    'css/style.css',
    'css/fonts/*',
    'img/**/*',
    'js/*.js'
}

-- Server Scripts
server_scripts {
    'final_hudv2/server/api.lua'
}

-- Client Scripts
client_scripts {
    'client.lua'
}

-- Dependencies (commented out for standalone use)
-- dependencies {
--     'es_extended' -- Uncomment if using ESX
--     'qb-core' -- Uncomment if using QB-Core
-- }

-- Exports (if needed)
exports {
    -- Add your exports here
}

-- Server Exports (if needed)
server_exports {
    -- Add your server exports here
}

-- Shared Scripts (if needed)
shared_scripts {
    -- Add your shared scripts here
}

-- Data Files (if needed)
data_file 'DLC_ITYP_REQUEST' 'stream/**/*.ytyp'

-- Lua 5.4 compatibility
lua54 'yes'